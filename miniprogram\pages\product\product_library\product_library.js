// pages/product/product_library/product_library.js
const api = require('../../../utils/api');
const productTypesBrands = require('../../../config/productTypesBrands');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 筛选相关
    productTypeOptions: [],
    brandOptions: [{ value: '', label: '全部品牌' }],
    productTypeIndex: -1,
    brandIndex: -1,
    selectedProductType: '',
    selectedBrand: '',
    
    // 产品列表
    products: [],
    loading: false,
    loadingMore: false,
    hasSearched: false,
    
    // 分页相关
    currentPage: 1,
    pageSize: 10,
    totalCount: 0,
    hasMore: false,
    
    // 滚动位置管理
    scrollTop: 0,
    isLoadingMore: false, // 用于区分是否在加载更多状态
    
    // 产品对比相关
    compareVisible: false, // 对比面板是否显示
    compareProducts: [] // 对比产品列表
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.initializeFilters();
    
    // 如果有传入的筛选参数，自动进行搜索
    if (options.productType || options.brand) {
      this.setFiltersFromOptions(options);
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时可以执行一些操作
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    if (this.data.hasSearched) {
      this.refreshProducts();
    } else {
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    // 使用 scroll-view 的 bindscrolltolower 事件替代
    // 保留此方法作为备用
  },

  /**
   * scroll-view 滚动到底部事件 - 已禁用自动加载
   */
  onScrollToLower() {
    // 不再自动加载更多，用户需要手动点击"查看更多产品"按钮
    console.log('滚动到底部，等待用户手动加载更多...');
    // 可以在这里添加一些提示或者其他操作
  },

  /**
   * 初始化筛选选项
   */
  initializeFilters() {
    try {
      // 初始化产品类型选项
      const productTypeOptions = [
        { value: '', label: '全部类型' }
      ];
      
      // 从配置文件中读取产品类型
      const productTypes = productTypesBrands.productTypes || {};
      Object.keys(productTypes).forEach(type => {
        let typeLabel = type;
        // 将英文类型转换为中文显示
        switch(type) {
          case 'phone':
            typeLabel = '手机';
            break;
          case 'laptop':
            typeLabel = '笔记本电脑';
            break;
          case 'tablet':
            typeLabel = '平板电脑';
            break;
          case 'headphones':
            typeLabel = '耳机';
            break;
          case 'smartwatch':
            typeLabel = '智能手表';
            break;
          default:
            typeLabel = type;
        }
        productTypeOptions.push({
          value: type,
          label: typeLabel
        });
      });

      this.setData({
        productTypeOptions,
        // 默认选中"全部类型"
        productTypeIndex: 0,
        selectedProductType: '',
        // 默认选中"全部品牌"
        brandIndex: 0,
        selectedBrand: ''
      });

      console.log('产品类型选项初始化完成:', productTypeOptions);
    } catch (error) {
      console.error('初始化筛选选项失败:', error);
      wx.showToast({
        title: '初始化失败',
        icon: 'error'
      });
    }
  },

  /**
   * 从传入参数设置筛选条件
   */
  setFiltersFromOptions(options) {
    const { productType, brand } = options;
    
    // 设置产品类型
    if (productType) {
      const typeIndex = this.data.productTypeOptions.findIndex(item => item.value === productType);
      if (typeIndex >= 0) {
        this.setData({
          productTypeIndex: typeIndex,
          selectedProductType: productType
        });
        this.updateBrandOptions(productType);
      }
    }
    
    // 设置品牌
    if (brand) {
      setTimeout(() => {
        const brandIndex = this.data.brandOptions.findIndex(item => item.value === brand);
        if (brandIndex >= 0) {
          this.setData({
            brandIndex: brandIndex,
            selectedBrand: brand
          });
        }
        // 自动搜索
        this.searchProducts();
      }, 100);
    } else if (productType) {
      // 只有产品类型时也自动搜索
      setTimeout(() => {
        this.searchProducts();
      }, 100);
    }
  },

  /**
   * 产品类型选择事件
   */
  onProductTypeSelect(e) {
    const { value, index } = e.currentTarget.dataset;
    const selectedOption = this.data.productTypeOptions[index];
    
    console.log('选择产品类型:', selectedOption);
    
    this.setData({
      productTypeIndex: index,
      selectedProductType: value,
      // 重置品牌选择
      brandIndex: -1,
      selectedBrand: ''
    });
    
    // 更新品牌选项
    this.updateBrandOptions(value);
  },

  /**
   * 品牌选择事件
   */
  onBrandSelect(e) {
    const { value, index } = e.currentTarget.dataset;
    const selectedOption = this.data.brandOptions[index];
    
    console.log('选择品牌:', selectedOption);
    
    this.setData({
      brandIndex: index,
      selectedBrand: value
    });
  },

  /**
   * 更新品牌选项
   */
  updateBrandOptions(productType) {
    try {
      const brandOptions = [
        { value: '', label: '全部品牌' }
      ];
      
      if (productType && productTypesBrands.productTypes[productType]) {
        const brands = productTypesBrands.productTypes[productType].brands || [];
        brands.forEach(brand => {
          // 过滤掉"未知品牌"
          if (brand !== '未知品牌') {
            brandOptions.push({
              value: brand,
              label: brand
            });
          }
        });
      }
      
      this.setData({
        brandOptions
      });
      
      console.log('品牌选项更新完成:', brandOptions);
    } catch (error) {
      console.error('更新品牌选项失败:', error);
    }
  },

  /**
   * 搜索产品
   */
  async searchProducts() {
    // 重置分页和滚动位置
    this.setData({
      currentPage: 1,
      products: [],
      scrollTop: 0,
      isLoadingMore: false
    });
    
    await this.loadProducts();
  },

  /**
   * 加载产品数据
   */
  async loadProducts() {
    if (this.data.loading) return;
    
    try {
      // 设置加载状态
      const updateData = {
        loading: this.data.currentPage === 1 ? true : false,
        loadingMore: this.data.currentPage > 1 ? true : false
      };
      
      this.setData(updateData);
      
      const { selectedProductType, selectedBrand, currentPage, pageSize } = this.data;
      
      console.log('加载产品参数:', {
        productType: selectedProductType,
        brandName: selectedBrand,
        page: currentPage,
        limit: pageSize
      });
      
      const result = await api.product.queryProducts(
        selectedProductType,
        selectedBrand,
        currentPage,
        pageSize
      );
      
      console.log('产品查询结果:', result);
      
      if (result.success) {
        const rawProducts = result.data.products || [];
        const pagination = result.data.pagination || {};
        
        // 预处理产品数据，将中文属性名转换为英文
        const newProducts = rawProducts.map(product => ({
          ...product,
          releaseDate: product['上市日期'] || product.releaseDate || '未知',
          // 确保每个产品都有唯一的ID，用于wx:key
          skuId: product.skuId || product.id || `product_${Date.now()}_${Math.random()}`
        }));
        
        // 如果是第一页，替换数据；否则追加数据
        const products = currentPage === 1 ? newProducts : [...this.data.products, ...newProducts];
        
        const finalUpdateData = {
          products,
          totalCount: pagination.total || 0,
          hasMore: pagination.hasNextPage || false,
          hasSearched: true,
          loading: false,
          loadingMore: false
        };
        
        // 如果是加载更多，不重置滚动位置
        if (currentPage === 1) {
          finalUpdateData.scrollTop = 0;
        }
        
        this.setData(finalUpdateData);
        
        // 更新产品列表中的对比状态
        this.updateProductsCompareStatus();
        
        // 提示信息
        if (newProducts.length === 0 && currentPage === 1) {
          wx.showToast({
            title: '暂无相关产品',
            icon: 'none'
          });
        } else if (currentPage === 1) {
          wx.showToast({
            title: `找到${pagination.total || 0}个产品`,
            icon: 'success'
          });
        } else if (newProducts.length > 0) {
          // 加载更多成功的提示
          console.log(`成功加载第${currentPage}页，新增${newProducts.length}个产品`);
          
          // 如果是手动加载更多，显示加载成功的数量
          if (currentPage > 1) {
            console.log(`手动加载更多成功：新增${newProducts.length}个产品`);
          }
        }
      } else {
        console.error('产品查询失败:', result);
        wx.showToast({
          title: result.message || '查询失败',
          icon: 'error'
        });
      }
    } catch (error) {
      console.error('加载产品失败:', error);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'error'
      });
    } finally {
      this.setData({ 
        loading: false,
        loadingMore: false
      });
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 加载更多
   */
  async loadMore() {
    if (!this.data.hasMore || this.data.loadingMore || this.data.loading) {
      console.log('无法加载更多:', {
        hasMore: this.data.hasMore,
        loadingMore: this.data.loadingMore,
        loading: this.data.loading
      });
      return;
    }
    
    console.log('开始加载更多数据...');
    
    this.setData({
      currentPage: this.data.currentPage + 1
    });
    
    await this.loadProducts();
  },

  /**
   * 手动加载更多产品 - 用户点击文字链接触发
   */
  async onLoadMoreClick() {
    if (!this.data.hasMore || this.data.loadingMore || this.data.loading) {
      console.log('无法加载更多:', {
        hasMore: this.data.hasMore,
        loadingMore: this.data.loadingMore,
        loading: this.data.loading
      });
      return;
    }
    
    console.log('用户点击查看更多文字链接，开始加载...');
    
    // 显示轻量加载提示
    wx.showToast({
      title: '加载中...',
      icon: 'loading',
      duration: 0 // 设置为0，手动控制隐藏时机
    });
    
    this.setData({
      currentPage: this.data.currentPage + 1
    });
    
    try {
      await this.loadProducts();
      
      // 加载完成后立即隐藏提示
      wx.hideToast();
    } catch (error) {
      console.error('手动加载更多失败:', error);
      
      // 隐藏加载提示
      wx.hideToast();
      
      // 显示错误提示
      wx.showToast({
        title: '加载失败',
        icon: 'error',
        duration: 1500
      });
      
      // 加载失败时回滚页码
      this.setData({
        currentPage: this.data.currentPage - 1
      });
    }
  },

  /**
   * 刷新产品列表
   */
  async refreshProducts() {
    this.setData({
      currentPage: 1,
      products: [],
      scrollTop: 0
    });
    
    await this.loadProducts();
  },

  /**
   * 重置筛选条件
   */
  resetFilters() {
    this.setData({
      productTypeIndex: -1,
      brandIndex: -1,
      selectedProductType: '',
      selectedBrand: '',
      brandOptions: [{ value: '', label: '全部品牌' }],
      products: [],
      hasSearched: false,
      currentPage: 1,
      totalCount: 0,
      hasMore: false,
      scrollTop: 0
    });
    
    wx.showToast({
      title: '筛选条件已重置',
      icon: 'success'
    });
  },

  /**
   * 产品点击事件
   */
  onProductTap(e) {
    const product = e.detail.product;
    console.log('点击产品:', product);
    
    // 跳转到产品详情页面
    wx.navigateTo({
      url: `/pages/product/product_detail/product_detail?productName=${encodeURIComponent(product.skuName)}`
    });
  },

  /**
   * 图片加载错误处理
   */
  onImageError(e) {
    console.log('图片加载失败:', e);
    // 可以设置默认图片或者隐藏图片
  },

  /**
   * 分享页面
   */
  onShareAppMessage() {
    return {
      title: '选选产品库 - 找到你想要的产品',
      path: '/pages/product/product_library/product_library'
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    return {
      title: '选选产品库 - 找到你想要的产品'
    };
  },

  // ==================== 产品对比功能 Product Compare Functions ====================

  /**
   * 切换对比面板显示状态
   */
  onToggleCompareVisible(e) {
    const { visible } = e.detail;
    this.setData({
      compareVisible: visible
    });
    console.log('对比面板显示状态:', visible);
  },

  /**
   * 添加产品到对比列表
   */
  addToCompare(product) {
    const { compareProducts } = this.data;
    const maxCount = 4;
    
    // 检查是否已存在
    const existIndex = compareProducts.findIndex(item => item.skuId === product.skuId);
    if (existIndex >= 0) {
      wx.showToast({
        title: '产品已在对比列表中',
        icon: 'none'
      });
      return;
    }
    
    // 检查数量限制
    if (compareProducts.length >= maxCount) {
      wx.showToast({
        title: `最多只能对比${maxCount}个产品`,
        icon: 'none'
      });
      return;
    }
    
    // 添加到对比列表
    const newCompareProducts = [...compareProducts, product];
    this.setData({
      compareProducts: newCompareProducts
    });
    
    // 更新产品列表中的对比状态
    this.updateProductsCompareStatus();
    
    wx.showToast({
      title: '已添加到对比列表',
      icon: 'success'
    });
    
    console.log('添加产品到对比列表:', product);
  },

  /**
   * 从对比列表移除产品
   */
  onRemoveCompareProduct(e) {
    const { product, index } = e.detail;
    console.log('移除对比产品:', product, index);
    
    let compareProducts;
    if (typeof index !== 'undefined') {
      // 通过索引移除
      compareProducts = [...this.data.compareProducts];
      compareProducts.splice(index, 1);
    } else {
      // 通过产品对象移除
      compareProducts = this.data.compareProducts.filter(p => p.skuId !== product.skuId);
    }
    
    this.setData({
      compareProducts
    });
    
    // 更新产品列表中的对比状态
    this.updateProductsCompareStatus();
    
    wx.showToast({
      title: '已移除',
      icon: 'success',
      duration: 1500
    });
  },

  /**
   * 清空所有对比产品
   */
  onClearAllCompareProducts() {
    this.setData({
      compareProducts: []
    });
    
    // 更新产品列表中的对比状态
    this.updateProductsCompareStatus();
    
    wx.showToast({
      title: '已清空对比列表',
      icon: 'success'
    });
    
    console.log('清空所有对比产品');
  },

  /**
   * 开始对比
   */
  onStartCompare(e) {
    const { products } = e.detail;
    
    if (products.length < 2) {
      wx.showToast({
        title: '至少选择2个产品进行对比',
        icon: 'none'
      });
      return;
    }
    
    console.log('开始对比产品:', products);
    
    // 构建对比页面参数
    const productIds = products.map(p => p.skuId).join(',');
    const productNames = products.map(p => p.skuName).join(',');
    
    // 跳转到对比结果页面
    wx.navigateTo({
      url: `/pages/result/result?productIds=${encodeURIComponent(productIds)}&productNames=${encodeURIComponent(productNames)}&source=compare`
    });
  },

  /**
   * 开始V4版本对比
   */
  onStartCompareV4(e) {
    const { products } = e.detail;
    
    if (products.length < 2) {
      wx.showToast({
        title: '至少选择2个产品进行对比',
        icon: 'none'
      });
      return;
    }
    
    if (products.length > 6) {
      wx.showToast({
        title: 'V4对比最多支持6个产品',
        icon: 'none'
      });
      return;
    }
    
    console.log('开始V4对比产品:', products);
    
    // 构建V4对比页面参数
    const productNames = products.map(p => p.skuName);
    
    // 跳转到V4对比结果页面
    wx.navigateTo({
      url: `/pages/product/product_compare_v4/product_compare_v4?productNames=${encodeURIComponent(JSON.stringify(productNames))}`
    });
  },

  /**
   * 对比产品点击事件
   */
  onCompareProductTap(e) {
    const { product } = e.detail;
    console.log('点击对比产品:', product);
    
    // 跳转到产品详情页面
    wx.navigateTo({
      url: `/pages/product/product_detail/product_detail?productName=${encodeURIComponent(product.skuName)}`
    });
  },

  /**
   * 产品对比按钮切换事件
   */
  onCompareToggle(e) {
    const { product } = e.detail;
    console.log('切换产品对比状态:', product);
    
    // 检查产品是否已在对比列表中
    const isInCompare = this.data.compareProducts.some(p => p.skuId === product.skuId);
    
    if (isInCompare) {
      // 从对比列表中移除
      const index = this.data.compareProducts.findIndex(p => p.skuId === product.skuId);
      this.onRemoveCompareProduct({ detail: { index } });
    } else {
      // 添加到对比列表
      this.addToCompare(product);
    }
  },

  /**
   * 更新产品列表中的对比状态
   */
  updateProductsCompareStatus() {
    const compareSkuIds = this.data.compareProducts.map(p => p.skuId);
    const updatedProducts = this.data.products.map(product => ({
      ...product,
      isInCompare: compareSkuIds.includes(product.skuId)
    }));
    
    this.setData({
      products: updatedProducts
    });
  }
});
